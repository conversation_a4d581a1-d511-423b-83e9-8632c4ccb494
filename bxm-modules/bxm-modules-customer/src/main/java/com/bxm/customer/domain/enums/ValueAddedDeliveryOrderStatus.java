package com.bxm.customer.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 增值交付单状态枚举
 * 
 * 状态流程说明：
 * 1. 基础流程：DRAFT → PENDING_CONFIRMATION → CONFIRMED_PENDING_DEDUCTION → DEDUCTION_COMPLETED
 * 2. 扣款异常流程：DEDUCTION_EXCEPTION → DEDUCTION_CLOSED
 * 3. 交付流程：SAVED_PENDING_SUBMIT → SUBMITTED_PENDING_DELIVERY → DELIVERY_COMPLETED
 * 4. 交付异常流程：DELIVERY_EXCEPTION → DELIVERY_CLOSED
 *
 * <AUTHOR>
 * @date 2025-08-03
 */
@Getter
@AllArgsConstructor
public enum ValueAddedDeliveryOrderStatus {

    /**
     * 草稿状态 - 初始状态
     */
    DRAFT("DRAFT", "草稿"),

    /**
     * 已交付待确认
     */
    PENDING_CONFIRMATION("PENDING_CONFIRMATION", "已交付待确认"),

    /**
     * 已确认待扣款
     */
    CONFIRMED_PENDING_DEDUCTION("CONFIRMED_PENDING_DEDUCTION", "已确认待扣款"),

    /**
     * 扣款中
     */
    DEDUCTION_PROCESSING("DEDUCTION_PROCESSING", "扣款中"),

    /**
     * 已扣款 - 正常完成状态
     */
    DEDUCTION_COMPLETED("DEDUCTION_COMPLETED", "已扣款"),

    /**
     * 扣款异常(待确认)
     */
    DEDUCTION_EXCEPTION("DEDUCTION_EXCEPTION", "扣款异常(待确认)"),

    /**
     * 已关闭扣款
     */
    DEDUCTION_CLOSED("DEDUCTION_CLOSED", "已关闭扣款"),

    /**
     * 已保存待提交
     */
    SAVED_PENDING_SUBMIT("SAVED_PENDING_SUBMIT", "已保存待提交"),

    /**
     * 已提交待交付
     */
    SUBMITTED_PENDING_DELIVERY("SUBMITTED_PENDING_DELIVERY", "已提交待交付"),

    /**
     * 交付中
     */
    DELIVERY_PROCESSING("DELIVERY_PROCESSING", "交付中"),

    /**
     * 交付完成
     */
    DELIVERY_COMPLETED("DELIVERY_COMPLETED", "交付完成"),

    /**
     * 交付异常(待确认)
     */
    DELIVERY_EXCEPTION("DELIVERY_EXCEPTION", "交付异常(待确认)"),

    /**
     * 已关闭交付
     */
    DELIVERY_CLOSED("DELIVERY_CLOSED", "已关闭交付"),

    /**
     * 已完成 - 订单整体完成
     */
    COMPLETED("COMPLETED", "已完成");

    /**
     * 状态代码（字符串值）
     */
    private final String code;

    /**
     * 状态描述
     */
    private final String description;

    /**
     * 根据状态代码获取枚举
     *
     * @param code 状态代码
     * @return 对应的枚举值，如果不存在则返回null
     */
    public static ValueAddedDeliveryOrderStatus getByCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            return null;
        }
        for (ValueAddedDeliveryOrderStatus status : values()) {
            if (status.getCode().equals(code.trim())) {
                return status;
            }
        }
        return null;
    }

    /**
     * 验证状态代码是否有效
     *
     * @param code 状态代码
     * @return 是否有效
     */
    public static boolean isValid(String code) {
        return getByCode(code) != null;
    }

    /**
     * 获取默认初始状态
     *
     * @return 默认状态（草稿）
     */
    public static ValueAddedDeliveryOrderStatus getDefaultStatus() {
        return DRAFT;
    }

    /**
     * 判断是否为终态（不可再变更的状态）
     *
     * @return 是否为终态
     */
    public boolean isFinalStatus() {
        return this == DEDUCTION_COMPLETED ||
               this == DEDUCTION_CLOSED ||
               this == DELIVERY_COMPLETED ||
               this == DELIVERY_CLOSED ||
               this == COMPLETED;
    }

    /**
     * 判断是否为异常状态
     *
     * @return 是否为异常状态
     */
    public boolean isExceptionStatus() {
        return this == DEDUCTION_EXCEPTION || this == DELIVERY_EXCEPTION;
    }

    /**
     * 判断是否为处理中状态
     *
     * @return 是否为处理中状态
     */
    public boolean isProcessingStatus() {
        return this == DEDUCTION_PROCESSING || this == DELIVERY_PROCESSING;
    }
}
